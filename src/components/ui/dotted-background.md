# DottedBackground Component

A reusable React component that creates a uniform dotted background pattern with fade edges and consistent spacing.

## Features

- **Uniform dot pattern**: All dots are the same size with consistent spacing
- **Adjustable spacing**: Control space between dots
- **Fade edges**: Configurable fade percentage for smooth edge transitions
- **Static design**: No animations or random variations for clean, predictable patterns
- **Dark mode support**: Separate opacity settings for light and dark themes
- **Flexible grid**: Adjustable number of dots per row/column
- **Color customization**: Set dot color via hex value

## Usage

```tsx
import { DottedBackground } from "@/components/ui/dotted-background";

// Basic usage
<DottedBackground />

// Customized usage
<DottedBackground
  fadeEdge={85}
  dotSize={2}
  spacing={20}
  dotsPerRow={8}
  opacity={0.3}
  darkOpacity={0.4}
  dotColor="888888"
/>
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `fadeEdge` | `number` | `80` | Fade edge percentage (0-100) |
| `dotSize` | `number` | `2` | Dot size in pixels |
| `spacing` | `number` | `20` | Space between dots in pixels |
| `dotsPerRow` | `number` | `8` | Number of dots per row/column |
| `opacity` | `number` | `0.3` | Base opacity for light mode |
| `darkOpacity` | `number` | `0.4` | Base opacity for dark mode |
| `dotColor` | `string` | `"888888"` | Dot color (hex without #) |
| `className` | `string` | `""` | Additional CSS classes |

## Examples

### Dashboard Background

```tsx
<DottedBackground
  fadeEdge={85}
  dotSize={2}
  spacing={20}
  dotsPerRow={8}
  opacity={0.3}
  darkOpacity={0.4}
  dotColor="888888"
/>
```

### Landing Page Background

```tsx
<DottedBackground
  fadeEdge={90}
  dotSize={1.5}
  spacing={25}
  dotsPerRow={6}
  opacity={0.2}
  darkOpacity={0.3}
  dotColor="999999"
/>
```

### Subtle Background

```tsx
<DottedBackground
  fadeEdge={95}
  dotSize={1}
  spacing={30}
  dotsPerRow={10}
  opacity={0.1}
  darkOpacity={0.15}
  dotColor="CCCCCC"
/>
```

## Implementation Notes

- The component generates a uniform grid of static dots with consistent spacing
- Fade edges are created using multiple gradient overlays for smooth transitions
- The pattern tiles seamlessly across the entire background
- All positioning is absolute, so ensure parent has `relative` positioning
- The component is optimized for performance with SVG-based patterns
- No animations or random variations for predictable, clean patterns

## Current Usage

- **Dashboard**: `/user-dashboard` - Medium visibility dots with 20px spacing
- **Landing Page**: `/` - Subtle dots with 25px spacing and high fade
- **Home Page**: Same as landing page

## Customization Tips

- **Subtle effect**: Use high `fadeEdge` (90+), low `opacity` (0.1-0.2)
- **Prominent effect**: Use lower `fadeEdge` (70-80), higher `opacity` (0.3-0.5)
- **Dense pattern**: Decrease `spacing`, increase `dotsPerRow`
- **Sparse pattern**: Increase `spacing`, decrease `dotsPerRow`
- **Larger dots**: Increase `dotSize` for more prominent dots
- **Smaller dots**: Decrease `dotSize` for more subtle effect
