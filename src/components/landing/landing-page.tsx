"use client";

import { Footer } from "@/components/layout/footer";
import { Testimonials } from "@/components/ui/testimonials";
import { WaitlistSection } from "@/components/ui/waitlist-section";
import { DottedBackground } from "@/components/ui/dotted-background";
import { FeatureGrid } from "./feature-grid";
import { HeroSection } from "./hero-section";
import { BlogSection } from "@/components/blog/blog-section";
import { getLatestPosts } from "@/data/blog-posts";

export function LandingPage() {
  const latestPosts = getLatestPosts(6);

  return (
    <div className="min-h-screen flex flex-col bg-background relative overflow-hidden">
      {/* Dotted Background Pattern */}
      <DottedBackground
        fadeEdge={90}
        minSize={0.8}
        maxSize={2.5}
        margin={15}
        dotsPerRow={5}
        opacity={0.2}
        darkOpacity={0.3}
        dotColor="999999"
      />

      <main className="flex-1 relative z-10">
        <HeroSection />
        <FeatureGrid />
        <BlogSection posts={latestPosts} />
        <Testimonials />
        <WaitlistSection />
      </main>
      <Footer />
    </div>
  );
}
