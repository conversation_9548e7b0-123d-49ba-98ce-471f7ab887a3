"use client";

import React, { useMemo } from "react";

interface DottedBackgroundProps {
  /** Fade edge percentage (0-100) */
  fadeEdge?: number;
  /** Array of dot sizes in pixels - dots will randomly use one of these */
  dotSizes?: number[];
  /** Space between dots in pixels */
  spacing?: number;
  /** Number of dots per row/column in pattern */
  dotsPerRow?: number;
  /** Base opacity for the entire pattern */
  opacity?: number;
  /** Dark mode opacity multiplier */
  darkOpacity?: number;
  /** Array of dot colors for light mode (hex without #) */
  lightColors?: string[];
  /** Array of dot colors for dark mode (hex without #) */
  darkColors?: string[];
  /** Additional CSS classes */
  className?: string;
}

export function DottedBackground({
  fadeEdge = 80,
  dotSizes = [2],
  spacing = 20,
  dotsPerRow = 8,
  opacity = 0.3,
  darkOpacity = 0.4,
  lightColors = ["888888"],
  darkColors = ["AAAAAA"],
  className = "",
}: DottedBackgroundProps) {
  // Seeded random function for consistent results
  const seededRandom = (seed: number) => {
    const x = Math.sin(seed) * 10000;
    return x - Math.floor(x);
  };

  // Generate uniform grid of static dots with deterministic "random" sizes and colors
  const { dots, patternSize } = useMemo(() => {
    const dots = [];
    const patternSize = dotsPerRow * spacing;

    // Create uniform grid of dots
    for (let row = 0; row < dotsPerRow; row++) {
      for (let col = 0; col < dotsPerRow; col++) {
        const x = (col * spacing) + (spacing / 2);
        const y = (row * spacing) + (spacing / 2);

        // Use position-based seed for consistent "random" selection
        const seed = row * 1000 + col;
        const sizeIndex = Math.floor(seededRandom(seed) * dotSizes.length);
        const lightColorIndex = Math.floor(seededRandom(seed + 1) * lightColors.length);
        const darkColorIndex = Math.floor(seededRandom(seed + 2) * darkColors.length);

        const size = dotSizes[sizeIndex];
        const lightColor = lightColors[lightColorIndex];
        const darkColor = darkColors[darkColorIndex];

        dots.push({ x, y, size, lightColor, darkColor });
      }
    }

    return { dots, patternSize };
  }, [dotsPerRow, spacing, dotSizes, lightColors, darkColors]); // Only regenerate if these props change

  // Create SVG patterns for light and dark modes
  const createSVGPattern = (isDark = false) => {
    const dotsElements = dots.map((dot, index) => {
      const color = isDark ? dot.darkColor : dot.lightColor;
      return `<circle cx='${dot.x}' cy='${dot.y}' r='${dot.size}' fill='%23${color}'/>`;
    }).join('');

    return `url("data:image/svg+xml,%3Csvg width='${patternSize}' height='${patternSize}' viewBox='0 0 ${patternSize} ${patternSize}' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E${dotsElements}%3C/g%3E%3C/svg%3E")`;
  };

  const fadeOpacity = fadeEdge / 100;

  return (
    <>
      {/* Dotted Background Pattern - Light Mode */}
      <div
        className={`absolute inset-0 opacity-[${opacity}] dark:opacity-0 ${className}`}
        style={{
          backgroundImage: createSVGPattern(false),
        }}
      />

      {/* Dotted Background Pattern - Dark Mode */}
      <div
        className={`absolute inset-0 opacity-0 dark:opacity-[${darkOpacity}] ${className}`}
        style={{
          backgroundImage: createSVGPattern(true),
        }}
      />

      {/* Fade Edges */}
      <div
        className="absolute inset-0"
        style={{
          background: `linear-gradient(to right, hsl(var(--background) / ${fadeOpacity}) 0%, transparent 20%, transparent 80%, hsl(var(--background) / ${fadeOpacity}) 100%)`
        }}
      />
      <div
        className="absolute inset-0"
        style={{
          background: `linear-gradient(to bottom, hsl(var(--background) / ${fadeOpacity}) 0%, transparent 20%, transparent 80%, hsl(var(--background) / ${fadeOpacity}) 100%)`
        }}
      />

      {/* Corner Fades for Extra Smoothness */}
      <div
        className="absolute inset-0"
        style={{
          background: `linear-gradient(135deg, hsl(var(--background) / ${fadeOpacity * 0.8}) 0%, transparent 30%)`
        }}
      />
      <div
        className="absolute inset-0"
        style={{
          background: `linear-gradient(225deg, hsl(var(--background) / ${fadeOpacity * 0.8}) 0%, transparent 30%)`
        }}
      />
      <div
        className="absolute inset-0"
        style={{
          background: `linear-gradient(315deg, hsl(var(--background) / ${fadeOpacity * 0.8}) 0%, transparent 30%)`
        }}
      />
      <div
        className="absolute inset-0"
        style={{
          background: `linear-gradient(45deg, hsl(var(--background) / ${fadeOpacity * 0.8}) 0%, transparent 30%)`
        }}
      />
    </>
  );
}
