# DottedBackground Component

A reusable React component that creates a customizable dotted background pattern with fade edges and random dot variations.

## Features

- **Customizable dot sizes**: Set minimum and maximum dot sizes
- **Adjustable spacing**: Control margin between dots
- **Fade edges**: Configurable fade percentage for smooth edge transitions
- **Random variations**: Each dot has random size and opacity for organic feel
- **Dark mode support**: Separate opacity settings for light and dark themes
- **Flexible grid**: Adjustable number of dots per row/column
- **Color customization**: Set dot color via hex value

## Usage

```tsx
import { DottedBackground } from "@/components/ui/dotted-background";

// Basic usage
<DottedBackground />

// Customized usage
<DottedBackground 
  fadeEdge={85}
  minSize={1.0}
  maxSize={3.0}
  margin={12}
  dotsPerRow={4}
  opacity={0.3}
  darkOpacity={0.4}
  dotColor="888888"
/>
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `fadeEdge` | `number` | `80` | Fade edge percentage (0-100) |
| `minSize` | `number` | `1.0` | Minimum dot size in pixels |
| `maxSize` | `number` | `3.0` | Maximum dot size in pixels |
| `margin` | `number` | `10` | Margin between dots in pixels |
| `dotsPerRow` | `number` | `4` | Number of dots per row/column |
| `opacity` | `number` | `0.3` | Base opacity for light mode |
| `darkOpacity` | `number` | `0.4` | Base opacity for dark mode |
| `dotColor` | `string` | `"888888"` | Dot color (hex without #) |
| `className` | `string` | `""` | Additional CSS classes |

## Examples

### Dashboard Background
```tsx
<DottedBackground 
  fadeEdge={85}
  minSize={1.0}
  maxSize={3.0}
  margin={12}
  dotsPerRow={4}
  opacity={0.3}
  darkOpacity={0.4}
  dotColor="888888"
/>
```

### Landing Page Background
```tsx
<DottedBackground 
  fadeEdge={90}
  minSize={0.8}
  maxSize={2.5}
  margin={15}
  dotsPerRow={5}
  opacity={0.2}
  darkOpacity={0.3}
  dotColor="999999"
/>
```

### Subtle Background
```tsx
<DottedBackground 
  fadeEdge={95}
  minSize={0.5}
  maxSize={1.5}
  margin={20}
  dotsPerRow={6}
  opacity={0.1}
  darkOpacity={0.15}
  dotColor="CCCCCC"
/>
```

## Implementation Notes

- The component generates random dot sizes and opacity values for each dot
- Fade edges are created using multiple gradient overlays
- The pattern tiles seamlessly across the entire background
- All positioning is absolute, so ensure parent has `relative` positioning
- The component is optimized for performance with SVG-based patterns

## Current Usage

- **Dashboard**: `/user-dashboard` - Medium visibility dots
- **Landing Page**: `/` - Subtle dots with high fade
- **Home Page**: Same as landing page

## Customization Tips

- **Subtle effect**: Use high `fadeEdge` (90+), low `opacity` (0.1-0.2)
- **Prominent effect**: Use lower `fadeEdge` (70-80), higher `opacity` (0.3-0.5)
- **Dense pattern**: Decrease `margin`, increase `dotsPerRow`
- **Sparse pattern**: Increase `margin`, decrease `dotsPerRow`
- **Uniform dots**: Set `minSize` and `maxSize` to same value
