@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-brand: var(--brand);
  --color-brand-foreground: var(--brand-foreground);

  --font-sans: var(--font-sans);
  --font-mono: var(--font-mono);
  --font-serif: var(--font-serif);

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  --shadow-2xs: var(--shadow-2xs);
  --shadow-xs: var(--shadow-xs);
  --shadow-sm: var(--shadow-sm);
  --shadow: var(--shadow);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);
  --shadow-2xl: var(--shadow-2xl);
  --radius: 0.3rem;
  --tracking-tighter: calc(var(--tracking-normal) - 0.05em);
  --tracking-tight: calc(var(--tracking-normal) - 0.025em);
  --tracking-wide: calc(var(--tracking-normal) + 0.025em);
  --tracking-wider: calc(var(--tracking-normal) + 0.05em);
  --tracking-widest: calc(var(--tracking-normal) + 0.1em);
  --tracking-normal: var(--tracking-normal);
  --spacing: var(--spacing);
  --letter-spacing: var(--letter-spacing);
  --shadow-offset-y: var(--shadow-offset-y);
  --shadow-offset-x: var(--shadow-offset-x);
  --shadow-spread: var(--shadow-spread);
  --shadow-blur: var(--shadow-blur);
  --shadow-opacity: var(--shadow-opacity);
  --color-shadow-color: var(--shadow-color);
  --max-width-container: 80rem;

  --keyframes-appear: {
    "0%": { opacity: "0", transform: "translateY(10px)" },
    "100%": { opacity: "1", transform: "translateY(0)" }
  };
  --keyframes-appear-zoom: {
    "0%": { opacity: "0", transform: "scale(0.95)" },
    "100%": { opacity: "1", transform: "scale(1)" }
  };
  --animation-appear: "appear 0.5s ease-out forwards";
  --animation-appear-zoom: "appear-zoom 0.5s ease-out forwards";
}

:root {
  --background: oklch(1.0000 0 0);
  --foreground: oklch(0.3211 0 0);
  --card: oklch(1.0000 0 0);
  --card-foreground: oklch(0.3211 0 0);
  --popover: oklch(1.0000 0 0);
  --popover-foreground: oklch(0.3211 0 0);
  --primary: oklch(0.6412 0.1155 155.0013);
  --primary-foreground: oklch(1.0000 0 0);
  --secondary: oklch(0.4375 0.0929 159.3902);
  --secondary-foreground: oklch(1.0000 0 0);
  --muted: oklch(0.9166 0.0148 102.4717);
  --muted-foreground: oklch(0.5382 0 0);
  --accent: oklch(0.6412 0.1155 155.0013);
  --accent-foreground: oklch(1.0000 0 0);
  --destructive: oklch(0.6766 0.1260 25.1211);
  --destructive-foreground: oklch(1.0000 0 0);
  --border: oklch(0.8699 0 0);
  --input: oklch(0.8699 0 0);
  --ring: oklch(0.6412 0.1155 155.0013);
  --chart-1: oklch(0.6412 0.1155 155.0013);
  --chart-2: oklch(0.7196 0.0906 267.0774);
  --chart-3: oklch(0.8118 0.0701 218.3524);
  --chart-4: oklch(0.6019 0.0723 251.0410);
  --chart-5: oklch(0.5737 0.1247 152.5238);
  --sidebar: oklch(1.0000 0 0);
  --sidebar-foreground: oklch(0.3211 0 0);
  --sidebar-primary: oklch(0.6412 0.1155 155.0013);
  --sidebar-primary-foreground: oklch(1.0000 0 0);
  --sidebar-accent: oklch(1.0000 0 0);
  --sidebar-accent-foreground: oklch(0.3211 0 0);
  --sidebar-border: oklch(0.8699 0 0);
  --sidebar-ring: oklch(0.6412 0.1155 155.0013);
  --brand: 142 76% 36%;
  --brand-foreground: 142 76% 46%;
  --font-sans: var(--font-outfit), Outfit, sans-serif;
  --font-serif: Source Serif 4, serif;
  --font-mono: JetBrains Mono, monospace;
  --radius: 0.5rem;
  --shadow-2xs: none;
  --shadow-xs: none;
  --shadow-sm: none;
  --shadow: none;
  --shadow-md: none;
  --shadow-lg: none;
  --shadow-xl: none;
  --shadow-2xl: none;
  --tracking-normal: 0em;
  --spacing: 0.25rem;
  --shadow-color: oklch(0 0 0);
  --shadow-opacity: 0;
  --shadow-blur: 0px;
  --shadow-spread: 0px;
  --shadow-offset-x: 0;
  --shadow-offset-y: 1px;
  --letter-spacing: 0em;
}

.dark {
  --background: oklch(0.2393 0.0100 268.2607);
  --foreground: oklch(0.9219 0 0);
  --card: oklch(0.3337 0.0065 229.0172);
  --card-foreground: oklch(0.9219 0 0);
  --popover: oklch(0.3337 0.0065 229.0172);
  --popover-foreground: oklch(0.9219 0 0);
  --primary: oklch(0.6412 0.1155 155.0013);
  --primary-foreground: oklch(1.0000 0 0);
  --secondary: oklch(0.6019 0.0723 251.0410);
  --secondary-foreground: oklch(0.9219 0 0);
  --muted: oklch(0.3867 0 0);
  --muted-foreground: oklch(0.7155 0 0);
  --accent: oklch(0.6412 0.1155 155.0013);
  --accent-foreground: oklch(1.0000 0 0);
  --destructive: oklch(0.6766 0.1260 25.1211);
  --destructive-foreground: oklch(1.0000 0 0);
  --border: oklch(0.3867 0 0);
  --input: oklch(0.3867 0 0);
  --ring: oklch(0.6412 0.1155 155.0013);
  --chart-1: oklch(0.6412 0.1155 155.0013);
  --chart-2: oklch(0.6019 0.0723 251.0410);
  --chart-3: oklch(0.7196 0.0906 267.0774);
  --chart-4: oklch(0.8118 0.0701 218.3524);
  --chart-5: oklch(0.5737 0.1247 152.5238);
  --sidebar: oklch(0.2393 0.0100 268.2607);
  --sidebar-foreground: oklch(0.9219 0 0);
  --sidebar-primary: oklch(0.6412 0.1155 155.0013);
  --sidebar-primary-foreground: oklch(1.0000 0 0);
  --sidebar-accent: oklch(0.7196 0.0906 267.0774);
  --sidebar-accent-foreground: oklch(0.9219 0 0);
  --sidebar-border: oklch(0.3867 0 0);
  --sidebar-ring: oklch(0.6412 0.1155 155.0013);
  --brand: 142 76% 46%;
  --brand-foreground: 142 76% 36%;
  --font-sans: var(--font-outfit), Outfit, sans-serif;
  --font-serif: Source Serif 4, serif;
  --font-mono: JetBrains Mono, monospace;
  --radius: 0.5rem;
  --shadow-2xs: none;
  --shadow-xs: none;
  --shadow-sm: none;
  --shadow: none;
  --shadow-md: none;
  --shadow-lg: none;
  --shadow-xl: none;
  --shadow-2xl: none;
  --shadow-color: oklch(0 0 0);
  --shadow-opacity: 0;
  --shadow-blur: 0px;
  --shadow-spread: 0px;
  --shadow-offset-x: 0;
  --shadow-offset-y: 1px;
  --letter-spacing: 0em;
  --spacing: 0.25rem;
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
    letter-spacing: var(--tracking-normal);
  }
}

@layer utilities {
  .delay-100 {
    animation-delay: 100ms;
  }
  .delay-300 {
    animation-delay: 300ms;
  }
  .delay-700 {
    animation-delay: 700ms;
  }
  .delay-1000 {
    animation-delay: 1000ms;
  }
  
  /* Hover эффект для группы */
  .group:hover .group-hover\:translate-y-\[-2rem\] {
    --tw-translate-y: -2rem;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate))
      skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x))
      scaleY(var(--tw-scale-y));
  }
  
  .group:hover .group-hover\:opacity-100 {
    opacity: 1;
  }
  
  .max-w-container {
    max-width: var(--max-width-container);
  }
}

@layer components {
  /* Improved checkbox visibility in dark mode */
  .dark [data-state="unchecked"] {
    @apply border-zinc-600 bg-zinc-800;
  }

  .dark [data-state="unchecked"]:hover {
    @apply border-zinc-500 bg-zinc-700;
  }

  .dark [data-state="checked"] {
    @apply border-primary bg-primary;
  }

  /* Better input visibility in dark mode */
  .dark input[type="checkbox"] {
    @apply border-zinc-600 bg-zinc-800;
  }

  .dark input[type="checkbox"]:checked {
    @apply border-primary bg-primary;
  }

  /* Improved form controls */
  .dark .peer:disabled ~ * {
    @apply opacity-50;
  }

  /* Better select and input borders */
  .dark select,
  .dark input:not([type="checkbox"]):not([type="radio"]) {
    @apply border-zinc-700 bg-zinc-900;
  }

  .dark select:focus,
  .dark input:not([type="checkbox"]):not([type="radio"]):focus {
    @apply border-primary ring-primary/20;
  }
}

/* Toast Styling */
[data-sonner-toast][data-type="success"] {
  @apply bg-green-50 text-green-900 border-green-200;
}

[data-sonner-toast][data-type="error"] {
  @apply bg-red-50 text-red-900 border-red-200;
  background-color: rgb(254 242 242) !important;
  color: rgb(127 29 29) !important;
  border-color: rgb(254 202 202) !important;
}

[data-sonner-toast][data-type="error"] [data-description] {
  color: rgb(153 27 27) !important;
}

[data-sonner-toast][data-type="info"] {
  @apply bg-blue-50 text-blue-900 border-blue-200;
}

[data-sonner-toast][data-type="warning"] {
  @apply bg-yellow-50 text-yellow-900 border-yellow-200;
}

/* Dark mode toast styling */
.dark [data-sonner-toast][data-type="success"] {
  @apply bg-green-950 text-green-100 border-green-800;
}

.dark [data-sonner-toast][data-type="error"] {
  @apply bg-red-950 text-red-100 border-red-800;
  background-color: rgb(69 10 10) !important;
  color: rgb(254 226 226) !important;
  border-color: rgb(153 27 27) !important;
}

.dark [data-sonner-toast][data-type="error"] [data-description] {
  color: rgb(120, 248, 113) !important;
}

.dark [data-sonner-toast][data-type="info"] {
  @apply bg-blue-950 text-blue-100 border-blue-800;
}

.dark [data-sonner-toast][data-type="warning"] {
  @apply bg-yellow-950 text-yellow-100 border-yellow-800;
}

/* Custom toast classes */
.toast-success {
  @apply bg-green-50 text-green-900 border-green-200;
}

.toast-error {
  @apply bg-red-50 text-red-900 border-red-200;
}

.toast-info {
  @apply bg-blue-50 text-blue-900 border-blue-200;
}

.toast-warning {
  @apply bg-yellow-50 text-yellow-900 border-yellow-200;
}

/* Dark mode custom toast classes */
.dark .toast-success {
  @apply bg-green-950 text-green-100 border-green-800;
}

.dark .toast-error {
  @apply bg-red-950 text-red-100 border-red-800;
}

.dark .toast-info {
  @apply bg-blue-950 text-blue-100 border-blue-800;
}

.dark .toast-warning {
  @apply bg-yellow-950 text-yellow-100 border-yellow-800;
}

/* Project main content dots pattern */
.project-main-content {
  background-image: radial-gradient(circle, rgb(209 213 219 / 0.5) 1.5px, transparent 1.5px);
  background-size: 20px 20px;
  mask-image:
    linear-gradient(to right, transparent 0%, black 15%, black 85%, transparent 100%),
    linear-gradient(to bottom, transparent 0%, black 15%, black 85%, transparent 100%);
  mask-composite: intersect;
  -webkit-mask-image:
    linear-gradient(to right, transparent 0%, black 15%, black 85%, transparent 100%),
    linear-gradient(to bottom, transparent 0%, black 15%, black 85%, transparent 100%);
  -webkit-mask-composite: source-in;
}

.dark .project-main-content {
  background-image: radial-gradient(circle, rgb(75 85 99 / 0.3) 1.5px, transparent 1.5px);
  background-size: 20px 20px;
  mask-image:
    linear-gradient(to right, transparent 0%, black 15%, black 85%, transparent 100%),
    linear-gradient(to bottom, transparent 0%, black 15%, black 85%, transparent 100%);
  mask-composite: intersect;
  -webkit-mask-image:
    linear-gradient(to right, transparent 0%, black 15%, black 85%, transparent 100%),
    linear-gradient(to bottom, transparent 0%, black 15%, black 85%, transparent 100%);
  -webkit-mask-composite: source-in;
}

/* Blog content styling */
.prose {
  color: hsl(var(--foreground));
  max-width: none;
}

.prose h1,
.prose h2,
.prose h3,
.prose h4,
.prose h5,
.prose h6 {
  color: hsl(var(--foreground));
  font-weight: 600;
  margin-top: 2rem;
  margin-bottom: 1rem;
}

.prose h1 {
  font-size: 2.25rem;
  line-height: 2.5rem;
}

.prose h2 {
  font-size: 1.875rem;
  line-height: 2.25rem;
}

.prose h3 {
  font-size: 1.5rem;
  line-height: 2rem;
}

.prose p {
  margin-bottom: 1.25rem;
  line-height: 1.75;
}

.prose ul,
.prose ol {
  margin-bottom: 1.25rem;
  padding-left: 1.5rem;
}

.prose li {
  margin-bottom: 0.5rem;
}

.prose strong {
  font-weight: 600;
  color: hsl(var(--foreground));
}

.prose code {
  background-color: hsl(var(--muted));
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
}

.prose blockquote {
  border-left: 4px solid hsl(var(--border));
  padding-left: 1rem;
  margin: 1.5rem 0;
  font-style: italic;
  color: hsl(var(--muted-foreground));
}

/* Line clamp utilities */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}