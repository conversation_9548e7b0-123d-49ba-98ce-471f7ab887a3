"use client";

import React from "react";

interface DottedBackgroundProps {
  /** Fade edge percentage (0-100) */
  fadeEdge?: number;
  /** Dot size in pixels */
  dotSize?: number;
  /** Space between dots in pixels */
  spacing?: number;
  /** Number of dots per row/column in pattern */
  dotsPerRow?: number;
  /** Base opacity for the entire pattern */
  opacity?: number;
  /** Dark mode opacity multiplier */
  darkOpacity?: number;
  /** Dot color (hex without #) */
  dotColor?: string;
  /** Additional CSS classes */
  className?: string;
}

export function DottedBackground({
  fadeEdge = 80,
  dotSize = 2,
  spacing = 20,
  dotsPerRow = 8,
  opacity = 0.3,
  darkOpacity = 0.4,
  dotColor = "888888",
  className = "",
}: DottedBackgroundProps) {
  // Generate uniform grid of static dots
  const generateDots = () => {
    const dots = [];
    const patternSize = dotsPerRow * spacing;

    // Create uniform grid of dots
    for (let row = 0; row < dotsPerRow; row++) {
      for (let col = 0; col < dotsPerRow; col++) {
        const x = (col * spacing) + (spacing / 2);
        const y = (row * spacing) + (spacing / 2);

        dots.push({ x, y, size: dotSize });
      }
    }

    return { dots, patternSize };
  };

  const { dots, patternSize } = generateDots();

  // Create SVG pattern with static dots
  const createSVGPattern = () => {
    const dotsElements = dots.map((dot, index) =>
      `<circle cx='${dot.x}' cy='${dot.y}' r='${dot.size}'/>`
    ).join('');

    return `url("data:image/svg+xml,%3Csvg width='${patternSize}' height='${patternSize}' viewBox='0 0 ${patternSize} ${patternSize}' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23${dotColor}'%3E${dotsElements}%3C/g%3E%3C/g%3E%3C/svg%3E")`;
  };

  const fadeOpacity = fadeEdge / 100;

  return (
    <>
      {/* Dotted Background Pattern */}
      <div
        className={`absolute inset-0 opacity-[${opacity}] dark:opacity-[${darkOpacity}] ${className}`}
        style={{
          backgroundImage: createSVGPattern(),
        }}
      />

      {/* Fade Edges */}
      <div 
        className="absolute inset-0" 
        style={{
          background: `linear-gradient(to right, rgba(var(--background), ${fadeOpacity}) 0%, transparent 20%, transparent 80%, rgba(var(--background), ${fadeOpacity}) 100%)`
        }}
      />
      <div 
        className="absolute inset-0" 
        style={{
          background: `linear-gradient(to bottom, rgba(var(--background), ${fadeOpacity}) 0%, transparent 20%, transparent 80%, rgba(var(--background), ${fadeOpacity}) 100%)`
        }}
      />
      
      {/* Corner Fades for Extra Smoothness */}
      <div 
        className="absolute inset-0" 
        style={{
          background: `linear-gradient(135deg, rgba(var(--background), ${fadeOpacity * 0.8}) 0%, transparent 30%)`
        }}
      />
      <div 
        className="absolute inset-0" 
        style={{
          background: `linear-gradient(225deg, rgba(var(--background), ${fadeOpacity * 0.8}) 0%, transparent 30%)`
        }}
      />
      <div 
        className="absolute inset-0" 
        style={{
          background: `linear-gradient(315deg, rgba(var(--background), ${fadeOpacity * 0.8}) 0%, transparent 30%)`
        }}
      />
      <div 
        className="absolute inset-0" 
        style={{
          background: `linear-gradient(45deg, rgba(var(--background), ${fadeOpacity * 0.8}) 0%, transparent 30%)`
        }}
      />
    </>
  );
}
