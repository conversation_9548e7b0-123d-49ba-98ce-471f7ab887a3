"use client";

// Prevent static generation for this page
export const dynamic = 'force-dynamic';

import { ProtectedRoute } from "@/components/auth/protected-route";
import { DashboardHeader } from "@/components/dashboard/DashboardHeader";
import { DashboardHeroSection } from "@/components/dashboard/dashboard-hero-section";
import { DashboardProjectsSection } from "@/components/dashboard/dashboard-projects-section";
import { DashboardStatsCards } from "@/components/dashboard/dashboard-stats-cards";
import { ProjectCreationAnimation } from "@/components/project-creation/ProjectCreationAnimation";
import { DottedBackground } from "@/components/ui/dotted-background";
import { useClerkAuth } from "@/hooks/useClerkAuth";
import { useUser, useUserExists } from "@/hooks/queries/useUser";
import { useSyncUserToBackend } from "@/hooks/mutations/useUserMutations";
import { useProjectCreationStore } from "@/stores/projectCreationStore";
import { Loader2 } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect } from "react";



export default function UserDashboardPage() {
  const router = useRouter();
  const {
    user: clerkUser,
    isSignedIn,
    isLoaded,
  } = useClerkAuth();

  // TanStack Query hooks
  const {
    data: backendUser,
    isLoading: isUserLoading,
  } = useUser();

  const {
    data: userExists,
  } = useUserExists();

  const syncUser = useSyncUserToBackend();

  // Project creation state
  const { currentStep } = useProjectCreationStore();



  useEffect(() => {
    if (isLoaded) {
      if (!isSignedIn) {
        router.push("/");
        return;
      }

      // For now, all users stay on user dashboard
      // You can add role-based routing later if needed
    }
  }, [isSignedIn, isLoaded, router]);

  // Auto-sync user if they don't exist in backend
  useEffect(() => {
    // Only sync if:
    // 1. User is signed in
    // 2. User existence check is complete and user doesn't exist
    // 3. Backend user query failed (no user found)
    // 4. Not currently syncing
    if (
      isSignedIn &&
      userExists === false &&
      !backendUser &&
      !isUserLoading &&
      !syncUser.isPending
    ) {
      console.log('User not found in backend, auto-syncing...');
      syncUser.mutate();
    }
  }, [isSignedIn, userExists, backendUser, isUserLoading, syncUser]);

  // Show loading while checking authentication
  if (!isLoaded || !clerkUser) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-muted-foreground">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-background relative overflow-hidden">
        {/* Dotted Background Pattern */}
        <DottedBackground
          fadeEdge={85}
          minSize={1.0}
          maxSize={3.0}
          margin={12}
          dotsPerRow={4}
          opacity={0.3}
          darkOpacity={0.4}
          dotColor="888888"
        />

        {/* Noise Texture Overlay */}
        <div
          className="absolute inset-0 opacity-[0.02] dark:opacity-[0.04]"
          style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg viewBox='0 0 256 256' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.8' numOctaves='4' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E")`,
            backgroundSize: "256px 256px",
          }}
        />

        {/* Integrated Header */}
        <DashboardHeader />

        {/* Main Content */}
        <main className="relative z-10">
          {/* Hero Section */}
          <DashboardHeroSection />

          {/* Dashboard Content */}
          <div className="container mx-auto px-4 space-y-8 pb-8">
            <DashboardStatsCards />
            <DashboardProjectsSection />
          </div>
        </main>

        {/* Project Creation Animation Overlay */}
        {currentStep !== 'idle' && <ProjectCreationAnimation />}
      </div>
    </ProtectedRoute>
  );
}
