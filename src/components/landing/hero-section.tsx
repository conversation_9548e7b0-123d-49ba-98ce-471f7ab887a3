"use client";

import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Glow } from "@/components/ui/glow";
import { Logo } from "@/components/ui/logo";
import { ThemeToggle } from "@/components/theme-toggle";
import { useClerkAuth } from "@/hooks/useClerkAuth";
import { useAnalytics } from "@/hooks/useAnalytics";
import { ArrowRight, Sparkles, Target, Users, Zap } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { useProjectCreationStore } from "@/stores/projectCreationStore";
import { useProjectCreationStream } from "@/lib/mockEventStream";

export function HeroSection() {
  const { isSignedIn, user } = useClerkAuth();
  const { trackClick, trackCustomEvent } = useAnalytics();
  const router = useRouter();
  const [showStickyHeader, setShowStickyHeader] = useState(false);
  const [currentHintIndex, setCurrentHintIndex] = useState(0);
  const [displayedText, setDisplayedText] = useState("");
  const [isTyping, setIsTyping] = useState(true);
  const [projectInput, setProjectInput] = useState("");

  // Project creation store and stream for logged-in users
  const {
    setIdeaText,
    startCreation,
    setCurrentProgress,
    setTotalSteps,
    incrementCompletedSteps,
    setCurrentStep,
    setCreatedProject,
  } = useProjectCreationStore();

  const { startCreation: startStream, addEventListener, removeEventListener } = useProjectCreationStream();

  const hints = [
    "share your business idea & I’ll help bring it into reality",
    "Describe your next project idea",
    "What do you want to build?",
    "What's on your mind?",
  ];

  useEffect(() => {
    const handleScroll = () => {
      // Show sticky header when scrolled past the integrated header (approximately 100px)
      setShowStickyHeader(window.scrollY > 100);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  useEffect(() => {
    const currentHint = hints[currentHintIndex];
    let timeoutId: NodeJS.Timeout;

    if (isTyping) {
      // Typing effect
      if (displayedText.length < currentHint.length) {
        timeoutId = setTimeout(() => {
          setDisplayedText(currentHint.slice(0, displayedText.length + 1));
        }, 50); // Typing speed - faster
      } else {
        // Finished typing, wait before starting to delete
        timeoutId = setTimeout(() => {
          setIsTyping(false);
        }, 500); // Pause duration - shorter
      }
    } else {
      // Deleting effect
      if (displayedText.length > 0) {
        timeoutId = setTimeout(() => {
          setDisplayedText(displayedText.slice(0, -1));
        }, 25); // Deleting speed - much faster
      } else {
        // Finished deleting, move to next hint
        setCurrentHintIndex((prev) => (prev + 1) % hints.length);
        setIsTyping(true);
      }
    }

    return () => clearTimeout(timeoutId);
  }, [displayedText, isTyping, currentHintIndex, hints]);

  // Set up event stream listener for project creation
  useEffect(() => {
    if (!isSignedIn) return;

    const handleStreamEvent = (event: CustomEvent) => {
      const { type, data } = event.detail;

      switch (type) {
        case 'setup':
          setTotalSteps(data.totalSteps);
          break;
        case 'progress':
          setCurrentProgress(data);
          break;
        case 'step_complete':
          incrementCompletedSteps();
          break;
        case 'complete':
          setCreatedProject({
            id: data.projectId,
            name: data.name,
            description: data.description,
          });
          setCurrentStep('completed');
          break;
        case 'error':
          console.error('Project creation error:', data.error);
          break;
      }
    };

    addEventListener(handleStreamEvent);
    return () => removeEventListener(handleStreamEvent);
  }, [isSignedIn, addEventListener, removeEventListener, setCurrentProgress, setTotalSteps, incrementCompletedSteps, setCreatedProject, setCurrentStep]);

  const handleGetStarted = () => {
    // Track the generate project button click
    trackClick("generate-project-button", "hero-section");

    // Track custom event with project input details
    trackCustomEvent("project_generation_attempted", {
      user_signed_in: isSignedIn,
      project_input_length: projectInput.length,
      has_project_input: projectInput.trim().length > 0,
      redirect_destination: isSignedIn ? "dashboard" : "register"
    });

    if (isSignedIn && projectInput.trim()) {
      // If user is logged in and has project input, start project creation
      setIdeaText(projectInput);
      startCreation();
      startStream(projectInput);
      router.push("/user-dashboard");
    } else if (isSignedIn) {
      // If user is logged in but no input, just go to dashboard
      router.push("/user-dashboard");
    } else {
      // If user is not logged in, go to register
      router.push("/auth/register");
    }
  };

  const handleDashboardClick = () => {
    router.push("/user-dashboard");
  };

  return (
    <>
      {/* Sticky Header - appears when scrolling */}
      <header
        className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
          showStickyHeader
            ? "translate-y-0 opacity-100"
            : "-translate-y-full opacity-0"
        }`}
      >
        <div className="relative backdrop-blur-md border-b border-white/10 dark:border-black/10 bg-background/80">
          {/* Noise texture overlay for the sticky header */}
          <div
            className="absolute inset-0 opacity-[0.15] dark:opacity-[0.25] pointer-events-none"
            style={{
              backgroundImage: `url("data:image/svg+xml,%3Csvg viewBox='0 0 256 256' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.65' numOctaves='3' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E")`,
              backgroundSize: "256px 256px",
            }}
          />

          <div className="container mx-auto px-4 flex h-16 items-center justify-between relative z-10">
            {/* Left: Logo and App Title */}
            <Logo size={32} animated={false} showText={true} href="/" />

            {/* Right: Theme Toggle and Auth Buttons */}
            <div className="flex items-center gap-3">
              <ThemeToggle />

              {isSignedIn ? (
                <Button
                  onClick={handleDashboardClick}
                  className="bg-[#166534] hover:bg-[#166534]/90 text-white border"
                >
                  Dashboard
                </Button>
              ) : (
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    asChild
                    className="border hover:bg-[#166534]/10 hover:text-[#166534]"
                  >
                    <Link href="/auth/login">Login</Link>
                  </Button>
                  <Button
                    asChild
                    className="bg-[#166534] hover:bg-[#166534]/90 text-white border"
                  >
                    <Link href="/auth/register">Get Started</Link>
                  </Button>
                </div>
              )}
            </div>
          </div>
        </div>
      </header>

      <section className="relative overflow-hidden min-h-screen flex flex-col">
      {/* Enhanced Gradient Background with Multiple Layers */}
      <div className="absolute inset-0">
        {/* Primary Gradient */}
        <div className="absolute inset-0 bg-gradient-to-t from-[#0d542b] via-[#b7d9c2]  to-white dark:from-[#0d542b] dark:via-[#2D4135FF]/80 dark:to-black" />

        {/* Animated Gradient Overlay */}
        <div className="absolute inset-0 bg-gradient-to-br from-[#166534]/20 via-transparent to-[#22c55e]/20 dark:from-[#166534]/30 dark:via-transparent dark:to-[#22c55e]/30 animate-pulse" />

        {/* Radial Gradient for Depth */}
        <div className="absolute inset-0 bg-radial-gradient from-[#166534]/10 via-transparent to-transparent dark:from-[#166534]/20" />

        {/* Noise Texture Overlay */}
        <div
          className="absolute inset-0 opacity-[0.15] dark:opacity-[0.25]"
          style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg viewBox='0 0 256 256' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.65' numOctaves='3' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E")`,
            backgroundSize: "256px 256px",
          }}
        />

        {/* Floating Gradient Orbs */}
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-[#166534]/30 to-[#22c55e]/30 rounded-full blur-3xl animate-float" />
        <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-gradient-to-l from-[#166534]/20 to-[#22c55e]/20 rounded-full blur-3xl animate-float-delayed" />
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-gradient-to-br from-[#166534]/15 to-[#22c55e]/15 rounded-full blur-2xl animate-pulse-slow" />
      </div>

      {/* Integrated Header */}
      <header className="relative z-50 w-full">
        <div className="container mx-auto px-4 flex h-16 items-center justify-between">
          {/* Left: Logo and App Title */}
          <Logo size={32} animated={false} showText={true} href="/" />

          {/* Right: Theme Toggle and Auth Buttons */}
          <div className="flex items-center gap-3">
            <ThemeToggle />

            {isSignedIn ? (
              <Button
                onClick={handleDashboardClick}
                className="bg-[#166534] hover:bg-[#166534]/90 text-white border"
              >
                Dashboard
              </Button>
            ) : (
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  asChild
                  className="border hover:bg-[#166534]/10 hover:text-[#166534]"
                >
                  <Link href="/auth/login">Login</Link>
                </Button>
                <Button
                  asChild
                  className="bg-[#166534] hover:bg-[#166534]/90 text-white border"
                >
                  <Link href="/auth/register">Get Started</Link>
                </Button>
              </div>
            )}
          </div>
        </div>
      </header>

      {/* Hero Content */}
      <div className="relative z-10 container mx-auto px-4 text-center py-20 flex-1 flex items-center">
        <div className="w-full">
          {/* Hero Title with Logo */}
          <div className="mb-16">
          {/* Logo and Brand Name */}
          <div className="flex items-center justify-center gap-4 mb-8">
            <Logo size={84} animated={true} showText={true} textSize={96} animationSpeed={0.25} />
          </div>

          <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold text-foreground mb-6 leading-tight">
              stop guessing. build to last.
          </h1>
          <p className="text-lg md:text-xl leading-relaxed tracking-tight text-muted-foreground max-w-3xl mx-auto">
          AI that works with you step-by-step to turn innovative ideas to viable businesses.
          </p>
        </div>

        {/* CTA Input Area */}
        <div className="max-w-2xl mx-auto">
          <div className="group relative bg-card/80 backdrop-blur border rounded-2xl p-6 space-y-4 shadow-xl overflow-hidden">
            {/* Subtle gradient overlay on the card */}
            <div className="absolute inset-0 bg-gradient-to-br from-[#166534]/5 via-transparent to-[#22c55e]/5 pointer-events-none" />

            <div className="relative z-10">
              <div className="flex items-center gap-2 text-muted-foreground">
                <Target className="w-4 h-4" />
                <span className="text-sm">
                  Ask Siift to create a project that...
                </span>
              </div>

              <div className="relative mt-4">
                <div className="relative">
                  <textarea
                    placeholder={displayedText}
                    value={projectInput}
                    onChange={(e) => {
                      setProjectInput(e.target.value);
                      // Track when user starts typing
                      if (e.target.value.length === 1) {
                        trackCustomEvent("project_input_started", {
                          location: "hero-section"
                        });
                      }
                    }}
                    onFocus={() => trackCustomEvent("project_input_focused", { location: "hero-section" })}
                    className="w-full px-4 py-3 pr-16 rounded-lg border bg-background/50 backdrop-blur text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-[#166534]/20 focus:border-[#166534] resize-none min-h-[80px] transition-all duration-300 hover:shadow-lg hover:shadow-[#166534]/20 relative z-10"
                    rows={3}
                  />
                  <Button
                    onClick={handleGetStarted}
                    className="absolute right-3 bottom-4 bg-[#166534] hover:bg-[#166534]/90 text-white shadow-lg z-20"
                    size="icon"
                  >
                    <ArrowRight className="w-4 h-4" />
                  </Button>
                </div>
              </div>

              {/* Quick Action Badges */}
              <div className="flex flex-wrap gap-2 justify-center mt-4">
                <Badge
                  variant="outline"
                  className="text-xs hover:bg-[#166534]/10 hover:border-[#166534] transition-colors cursor-pointer"
                  onClick={() => {
                    trackClick("quick-action-badge", "hero-section");
                    trackCustomEvent("badge_clicked", { badge_type: "project_tracker", location: "hero-section" });
                    setProjectInput("Create a project tracker that manages team tasks and deadlines");
                  }}
                >
                  <Zap className="w-3 h-3 mr-1" />
                  Project tracker
                </Badge>
                <Badge
                  variant="outline"
                  className="text-xs hover:bg-[#166534]/10 hover:border-[#166534] transition-colors cursor-pointer"
                  onClick={() => {
                    trackClick("quick-action-badge", "hero-section");
                    trackCustomEvent("badge_clicked", { badge_type: "team_collaboration", location: "hero-section" });
                    setProjectInput("Create a team collaboration platform for better communication");
                  }}
                >
                  <Users className="w-3 h-3 mr-1" />
                  Team collaboration
                </Badge>
                <Badge
                  variant="outline"
                  className="text-xs hover:bg-[#166534]/10 hover:border-[#166534] transition-colors cursor-pointer"
                  onClick={() => {
                    trackClick("quick-action-badge", "hero-section");
                    trackCustomEvent("badge_clicked", { badge_type: "goal_management", location: "hero-section" });
                    setProjectInput("Create a goal management system to track objectives and milestones");
                  }}
                >
                  <Target className="w-3 h-3 mr-1" />
                  Goal management
                </Badge>
                <Badge
                  variant="outline"
                  className="text-xs hover:bg-[#166534]/10 hover:border-[#166534] transition-colors cursor-pointer"
                  onClick={() => {
                    trackClick("quick-action-badge", "hero-section");
                    trackCustomEvent("badge_clicked", { badge_type: "analytics_dashboard", location: "hero-section" });
                    setProjectInput("Create an analytics dashboard to visualize project performance");
                  }}
                >
                  <Sparkles className="w-3 h-3 mr-1" />
                  Analytics dashboard
                </Badge>
              </div>
            </div>

            {/* Glow effect that appears on hover */}
            <div className="absolute left-0 top-0 h-full w-full translate-y-[1rem] opacity-80 transition-all duration-500 ease-in-out group-hover:translate-y-[-2rem] group-hover:opacity-100">
              <Glow
                variant="bottom"
                className="animate-appear-zoom delay-300"
              />
            </div>
          </div>
        </div>
      </div>
      </div>

      {/* Custom CSS for animations */}
      <style jsx>{`
        @keyframes float {
          0%,
          100% {
            transform: translateY(0px) rotate(0deg);
          }
          50% {
            transform: translateY(-20px) rotate(180deg);
          }
        }

        @keyframes float-delayed {
          0%,
          100% {
            transform: translateY(0px) rotate(0deg);
          }
          50% {
            transform: translateY(15px) rotate(-180deg);
          }
        }

        @keyframes pulse-slow {
          0%,
          100% {
            opacity: 0.3;
            transform: scale(1);
          }
          50% {
            opacity: 0.6;
            transform: scale(1.1);
          }
        }

        .animate-float {
          animation: float 20s ease-in-out infinite;
        }

        .animate-float-delayed {
          animation: float-delayed 25s ease-in-out infinite;
        }

        .animate-pulse-slow {
          animation: pulse-slow 8s ease-in-out infinite;
        }

        .bg-radial-gradient {
          background: radial-gradient(
            circle at center,
            var(--tw-gradient-stops)
          );
        }
      `}</style>
    </section>
    </>
  );
}
